# SHA1:4434e62ddf002c09555be8d45138c3b3a04d0c29
#
# This file is autogenerated by pip-compile-multi
# To update, run:
#
#    pip-compile-multi
#
-e file:.
    # via -r requirements/base.in
alembic==1.13.1
    # via flask-migrate
amqp==5.2.0
    # via kombu
apispec[yaml]==6.3.0
    # via flask-appbuilder
apsw==********
    # via shillelagh
async-timeout==5.0.1
    # via redis
attrs==23.2.0
    # via
    #   cattrs
    #   jsonschema
    #   requests-cache
babel==2.15.0
    # via flask-babel
backoff==2.2.1
    # via apache-superset
bcrypt==4.1.3
    # via paramiko
billiard==4.2.0
    # via celery
blinker==1.8.2
    # via flask
bottleneck==1.3.8
    # via pandas
brotli==1.1.0
    # via flask-compress
cachelib==0.9.0
    # via
    #   flask-caching
    #   flask-session
cachetools==5.3.3
    # via google-auth
cattrs==23.2.3
    # via requests-cache
celery==5.4.0
    # via apache-superset
certifi==2024.2.2
    # via requests
cffi==1.16.0
    # via
    #   cryptography
    #   pynacl
charset-normalizer==3.3.2
    # via requests
click==8.1.7
    # via
    #   apache-superset
    #   celery
    #   click-didyoumean
    #   click-option-group
    #   click-plugins
    #   click-repl
    #   flask
    #   flask-appbuilder
click-didyoumean==0.3.1
    # via celery
click-option-group==0.5.6
    # via apache-superset
click-plugins==1.1.1
    # via celery
click-repl==0.3.0
    # via celery
colorama==0.4.6
    # via
    #   apache-superset
    #   flask-appbuilder
confluent-kafka==2.10.0
    # via -r requirements/base.in
cron-descriptor==1.4.3
    # via apache-superset
croniter==2.0.5
    # via apache-superset
cryptography==42.0.7
    # via
    #   apache-superset
    #   paramiko
    #   pyopenssl
defusedxml==0.7.1
    # via odfpy
deprecated==1.2.14
    # via limits
deprecation==2.1.0
    # via apache-superset
dnspython==2.6.1
    # via email-validator
email-validator==2.1.1
    # via flask-appbuilder
et-xmlfile==2.0.0
    # via openpyxl
exceptiongroup==1.2.2
    # via cattrs
flask==2.3.3
    # via
    #   apache-superset
    #   flask-appbuilder
    #   flask-babel
    #   flask-caching
    #   flask-compress
    #   flask-http-middleware
    #   flask-jwt-extended
    #   flask-limiter
    #   flask-login
    #   flask-migrate
    #   flask-session
    #   flask-sqlalchemy
    #   flask-wtf
flask-appbuilder==4.5.0
    # via apache-superset
flask-babel==2.0.0
    # via flask-appbuilder
flask-caching==2.3.0
    # via apache-superset
flask-compress==1.15
    # via apache-superset
flask-http-middleware==0.4.4
    # via -r requirements/base.in
flask-jwt-extended==4.6.0
    # via flask-appbuilder
flask-limiter==3.7.0
    # via flask-appbuilder
flask-login==0.6.3
    # via
    #   apache-superset
    #   flask-appbuilder
flask-migrate==3.1.0
    # via apache-superset
flask-session==0.8.0
    # via apache-superset
flask-sqlalchemy==2.5.1
    # via
    #   flask-appbuilder
    #   flask-migrate
flask-talisman==1.1.0
    # via apache-superset
flask-wtf==1.2.1
    # via
    #   apache-superset
    #   flask-appbuilder
func-timeout==4.3.5
    # via apache-superset
geographiclib==2.0
    # via geopy
geopy==2.4.1
    # via apache-superset
google-auth==2.29.0
    # via shillelagh
greenlet==3.0.3
    # via
    #   shillelagh
    #   sqlalchemy
gunicorn==22.0.0
    # via apache-superset
hashids==1.3.1
    # via apache-superset
holidays==0.25
    # via apache-superset
humanize==4.9.0
    # via apache-superset
idna==3.7
    # via
    #   email-validator
    #   requests
importlib-metadata==7.1.0
    # via apache-superset
importlib-resources==6.4.0
    # via limits
isodate==0.6.1
    # via apache-superset
itsdangerous==2.2.0
    # via
    #   flask
    #   flask-wtf
jinja2==3.1.4
    # via
    #   flask
    #   flask-babel
jsonpath-ng==1.6.1
    # via apache-superset
jsonschema==4.17.3
    # via flask-appbuilder
kombu==5.3.7
    # via celery
korean-lunar-calendar==0.3.1
    # via holidays
limits==3.12.0
    # via flask-limiter
llvmlite==0.42.0
    # via numba
mako==1.3.5
    # via
    #   alembic
    #   apache-superset
markdown==3.6
    # via apache-superset
markdown-it-py==3.0.0
    # via rich
markupsafe==2.1.5
    # via
    #   jinja2
    #   mako
    #   werkzeug
    #   wtforms
marshmallow==3.21.2
    # via
    #   flask-appbuilder
    #   marshmallow-enum
    #   marshmallow-sqlalchemy
marshmallow-enum==1.5.1
    # via -r requirements/base.in
marshmallow-sqlalchemy==0.28.2
    # via flask-appbuilder
mdurl==0.1.2
    # via markdown-it-py
msgpack==1.0.8
    # via apache-superset
msgspec==0.18.6
    # via flask-session
nh3==0.2.17
    # via apache-superset
numba==0.59.1
    # via pandas
numexpr==2.10.0
    # via
    #   -r requirements/base.in
    #   pandas
numpy==1.23.5
    # via
    #   apache-superset
    #   bottleneck
    #   numba
    #   numexpr
    #   pandas
    #   pyarrow
odfpy==1.4.1
    # via pandas
openpyxl==3.1.5
    # via pandas
ordered-set==4.1.0
    # via flask-limiter
packaging==23.2
    # via
    #   apache-superset
    #   apispec
    #   deprecation
    #   gunicorn
    #   limits
    #   marshmallow
    #   marshmallow-sqlalchemy
    #   shillelagh
pandas[excel,performance]==2.0.3
    # via apache-superset
paramiko==3.4.0
    # via
    #   apache-superset
    #   sshtunnel
parsedatetime==2.6
    # via apache-superset
pgsanity==0.2.9
    # via apache-superset
platformdirs==3.8.1
    # via requests-cache
ply==3.11
    # via jsonpath-ng
polyline==2.0.2
    # via apache-superset
prison==0.2.1
    # via flask-appbuilder
prometheus-client==0.21.1
    # via -r requirements/base.in
prompt-toolkit==3.0.44
    # via click-repl
pyarrow==14.0.2
    # via apache-superset
pyasn1==0.6.0
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.0
    # via google-auth
pycparser==2.22
    # via cffi
pygments==2.18.0
    # via rich
pyjwt==2.8.0
    # via
    #   apache-superset
    #   flask-appbuilder
    #   flask-jwt-extended
pynacl==1.5.0
    # via paramiko
pyopenssl==24.1.0
    # via shillelagh
pyparsing==3.1.2
    # via apache-superset
pyrsistent==0.20.0
    # via jsonschema
python-dateutil==2.9.0.post0
    # via
    #   apache-superset
    #   celery
    #   croniter
    #   flask-appbuilder
    #   holidays
    #   pandas
    #   shillelagh
python-dotenv==1.0.1
    # via apache-superset
python-geohash==0.8.5
    # via apache-superset
pytz==2024.1
    # via
    #   croniter
    #   flask-babel
    #   pandas
pyxlsb==1.0.10
    # via pandas
pyyaml==6.0.1
    # via
    #   apache-superset
    #   apispec
redis==4.6.0
    # via apache-superset
requests==2.32.2
    # via
    #   requests-cache
    #   shillelagh
requests-cache==1.2.0
    # via shillelagh
rich==13.7.1
    # via flask-limiter
rsa==4.9
    # via google-auth
selenium==3.141.0
    # via apache-superset
shillelagh[gsheetsapi]==1.2.18
    # via apache-superset
shortid==0.1.2
    # via apache-superset
simplejson==3.19.2
    # via apache-superset
six==1.16.0
    # via
    #   isodate
    #   prison
    #   python-dateutil
    #   url-normalize
    #   wtforms-json
slack-sdk==3.27.2
    # via apache-superset
sqlalchemy==1.4.52
    # via
    #   alembic
    #   apache-superset
    #   flask-appbuilder
    #   flask-sqlalchemy
    #   marshmallow-sqlalchemy
    #   shillelagh
    #   sqlalchemy-utils
sqlalchemy-utils==0.38.3
    # via
    #   apache-superset
    #   flask-appbuilder
sqlglot==25.24.0
    # via apache-superset
sqlparse==0.5.0
    # via apache-superset
sshtunnel==0.4.0
    # via apache-superset
tabulate==0.8.10
    # via apache-superset
typing-extensions==4.12.0
    # via
    #   alembic
    #   apache-superset
    #   cattrs
    #   flask-limiter
    #   limits
    #   shillelagh
tzdata==2024.1
    # via
    #   celery
    #   pandas
url-normalize==1.4.3
    # via requests-cache
urllib3==1.26.18
    # via
    #   -r requirements/base.in
    #   requests
    #   requests-cache
    #   selenium
vine==5.1.0
    # via
    #   amqp
    #   celery
    #   kombu
wcwidth==0.2.13
    # via prompt-toolkit
werkzeug==3.0.6
    # via
    #   -r requirements/base.in
    #   flask
    #   flask-appbuilder
    #   flask-http-middleware
    #   flask-jwt-extended
    #   flask-login
wrapt==1.16.0
    # via deprecated
wtforms==3.1.2
    # via
    #   apache-superset
    #   flask-appbuilder
    #   flask-wtf
    #   wtforms-json
wtforms-json==0.3.5
    # via apache-superset
xlrd==2.0.1
    # via pandas
xlsxwriter==3.0.9
    # via
    #   apache-superset
    #   pandas
zipp==3.19.0
    # via importlib-metadata
zstandard==0.22.0
    # via flask-compress
