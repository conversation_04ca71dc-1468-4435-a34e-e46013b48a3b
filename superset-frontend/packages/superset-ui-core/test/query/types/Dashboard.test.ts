// DODO was here
import {
  isNativeFilter,
  isFilterDivider,
  Filter,
  NativeFilterType,
  FilterWithDataMask,
  Divider,
  isNativeFilterWithDataMask,
  isAppliedCrossFilterType,
  isAppliedNativeFilterType,
  AppliedCrossFilterType,
  AppliedNativeFilterType,
} from '@superset-ui/core';

const filter: Filter = {
  cascadeParentIds: [],
  defaultDataMask: {},
  id: 'filter_id',
  name: 'Filter Name',
  scope: { rootPath: [], excluded: [] },
  filterType: 'filter_type',
  targets: [{}],
  controlValues: {},
  type: NativeFilterType.NativeFilter,
  description: 'Filter description.',
};

const filterWithDataMask: FilterWithDataMask = {
  ...filter,
  dataMask: { id: 'data_mask_id', filterState: { value: 'Filter value' } },
};

const filterDivider: Divider = {
  id: 'divider_id',
  type: NativeFilterType.Divider,
  title: 'Divider title',
  description: 'Divider description.',
  titleRu: 'Divider title ru', // DODO added 55319760
  descriptionRu: 'Divider description ru.', // DODO added 55319760
};

const appliedCrossFilter: AppliedCrossFilterType = {
  filterType: undefined,
  targets: [1, 2],
  scope: [1, 2],
  values: null,
};

const appliedNativeFilter: AppliedNativeFilterType = {
  filterType: 'filter_select',
  scope: [1, 2],
  targets: [{}],
  values: null,
};

test('filter type guard', () => {
  expect(isNativeFilter(filter)).toBeTruthy();
  expect(isNativeFilter(filterWithDataMask)).toBeTruthy();
  expect(isNativeFilter(filterDivider)).toBeFalsy();
});

test('filter with dataMask type guard', () => {
  expect(isNativeFilterWithDataMask(filter)).toBeFalsy();
  expect(isNativeFilterWithDataMask(filterWithDataMask)).toBeTruthy();
  expect(isNativeFilterWithDataMask(filterDivider)).toBeFalsy();
});

test('filter divider type guard', () => {
  expect(isFilterDivider(filter)).toBeFalsy();
  expect(isFilterDivider(filterWithDataMask)).toBeFalsy();
  expect(isFilterDivider(filterDivider)).toBeTruthy();
});

test('applied cross filter type guard', () => {
  expect(isAppliedCrossFilterType(appliedCrossFilter)).toBeTruthy();
  expect(isAppliedCrossFilterType(appliedNativeFilter)).toBeFalsy();
});

test('applied native filter type guard', () => {
  expect(isAppliedNativeFilterType(appliedNativeFilter)).toBeTruthy();
  expect(isAppliedNativeFilterType(appliedCrossFilter)).toBeFalsy();
});
