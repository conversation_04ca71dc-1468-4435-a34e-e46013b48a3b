// DODO was here
import { FC } from 'react';
import { FormItem } from 'src/components/Form';
import { Input, TextArea } from 'src/components/Input';
import { NativeFilterType, styled, t } from '@superset-ui/core';

interface DividerDodoExtended {
  titleRu: string; // DODO added 55319760
  descriptionRu: string; // DODO added 55319760
}
interface Props {
  componentId: string;
  divider?: {
    title: string;
    description: string;
  } & DividerDodoExtended;
}
const Container = styled.div`
  ${({ theme }) => `
    padding: ${theme.gridUnit * 4}px;
  `}
`;

const DividerConfigForm: FC<Props> = ({ componentId, divider }) => (
  <Container>
    {/* DODO added 55319760 */}
    <h4>{t('English')}</h4>
    <FormItem
      initialValue={divider ? divider.title : ''}
      label={t('Title')}
      name={['filters', componentId, 'title']}
      rules={[
        { required: true, message: t('Title is required'), whitespace: true },
      ]}
    >
      <Input />
    </FormItem>
    <FormItem
      initialValue={divider ? divider.description : ''}
      label={t('Description')}
      name={['filters', componentId, 'description']}
    >
      <TextArea rows={4} />
    </FormItem>
    <FormItem
      hidden
      name={['filters', componentId, 'type']}
      initialValue={NativeFilterType.Divider}
    />
    {/* DODO added start 55319760 */}
    <h4>{t('Russian')}</h4>
    <FormItem
      initialValue={divider ? divider.titleRu : ''}
      label={t('Title')}
      name={['filters', componentId, 'titleRu']}
    >
      <Input />
    </FormItem>
    <FormItem
      initialValue={divider ? divider.descriptionRu : ''}
      label={t('Description')}
      name={['filters', componentId, 'descriptionRu']}
    >
      <TextArea rows={4} />
    </FormItem>
    {/* DODO added stop 55319760 */}
  </Container>
);

export default DividerConfigForm;
