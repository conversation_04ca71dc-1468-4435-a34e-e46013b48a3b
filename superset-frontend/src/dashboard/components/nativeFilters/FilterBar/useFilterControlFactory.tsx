// DODO was here

import { useCallback, useMemo } from 'react';
import {
  DataMask,
  DataMaskStateWithId,
  Divider,
  Filter,
  isFilterDivider,
} from '@superset-ui/core';
import { FilterBarOrientation } from 'src/dashboard/types';
import { bootstrapData } from 'src/preamble'; // DODO added 55319760
import FilterControl from './FilterControls/FilterControl';
import { useFilters } from './state';
import FilterDivider from './FilterControls/FilterDivider';

const locale = bootstrapData?.common?.locale || 'en'; // DODO added 55319760

export const useFilterControlFactory = (
  dataMaskSelected: DataMaskStateWithId,
  onFilterSelectionChange: (filter: Filter, dataMask: DataMask) => void,
) => {
  const filters = useFilters();
  const filterValues = useMemo(() => Object.values(filters), [filters]);
  const filtersWithValues: (Filter | Divider)[] = useMemo(
    () =>
      filterValues.map(filter => ({
        ...filter,
        dataMask: dataMaskSelected[filter.id],
      })),
    [filterValues, dataMaskSelected],
  );

  const filterControlFactory = useCallback(
    (
      index: number,
      filterBarOrientation: FilterBarOrientation,
      overflow: boolean,
    ) => {
      const filter = filtersWithValues[index];
      if (isFilterDivider(filter)) {
        // DODO added start 55319760
        const { title, description, titleRu, descriptionRu } = filter;
        const localisedTitle =
          locale === 'ru' ? titleRu || title : title || titleRu;
        const localisedDescription =
          locale === 'ru'
            ? descriptionRu || description
            : description || descriptionRu;
        // DODO added stop 55319760
        return (
          <FilterDivider
            title={localisedTitle} // DODO changed 55319760
            description={localisedDescription} // DODO changed 55319760
            orientation={filterBarOrientation}
            overflow={overflow}
          />
        );
      }
      return (
        <FilterControl
          dataMaskSelected={dataMaskSelected}
          filter={filter}
          onFilterSelectionChange={onFilterSelectionChange}
          inView={false}
          orientation={filterBarOrientation}
          overflow={overflow}
        />
      );
    },
    [filtersWithValues, dataMaskSelected, onFilterSelectionChange],
  );

  return { filterControlFactory, filtersWithValues };
};
