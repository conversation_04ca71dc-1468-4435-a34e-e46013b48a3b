// DOD<PERSON> was here
/* eslint-env browser */
import { rgba } from 'emotion-rgba';
import Tabs from 'src/components/Tabs';
import {
  t,
  css,
  SupersetTheme,
  styled,
  SupersetClient,
} from '@superset-ui/core';
import { useToasts } from 'src/components/MessageToasts/withToasts';
import SliceAdder from 'src/dashboard/containers/SliceAdder';
import dashboardComponents from 'src/visualizations/presets/dashboardComponents';
// DODO added start 55339193
import { CssEditor as AceCssEditor } from 'src/components/AsyncAceEditor';
import { Key, useEffect, useState } from 'react';
import { RootState } from 'src/dashboard/types';
import { useDispatch, useSelector } from 'react-redux';
import { Menu } from 'src/components/Menu';
import { AntdDropdown } from 'src/components';
import Button from 'src/components/Button';
import { onChange, updateCss } from 'src/dashboard/actions/dashboardState';
import injectCustomCss from 'src/dashboard/util/injectCustomCss';
import DashboardHelper from 'src/DodoExtensions/dashboard/components/DashboardHelper';
// DODO added stop 55339193
import NewColumn from '../gridComponents/new/NewColumn';
import NewDivider from '../gridComponents/new/NewDivider';
import NewHeader from '../gridComponents/new/NewHeader';
import NewRow from '../gridComponents/new/NewRow';
import NewTabs from '../gridComponents/new/NewTabs';
import NewMarkdown from '../gridComponents/new/NewMarkdown';
import NewDynamicComponent from '../gridComponents/new/NewDynamicComponent';

const BUILDER_PANE_WIDTH = 374;

// DODO added 55339193
interface Template {
  value: string;
  css: string;
  label: string;
}

// DODO added 55339193
const StyledCssWrapper = styled.div`
  ${({ theme }) => css`
    padding-inline: ${theme.gridUnit * 4}px;
    height: 100%;

    .css-editor-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: ${theme.gridUnit * 2}px;
    }

    h5 {
      margin: 0;
    }
  `}
`;

const BuilderComponentPane = ({ topOffset = 0 }) => {
  // DODO added start 55339193
  const initialCss = useSelector(
    (state: RootState) => state.dashboardState.css || '',
  );
  const [templates, setTemplates] = useState<Template[] | null>(null);
  const [activeTab, setActiveTab] = useState('1');
  const [cssValue, setCssValue] = useState(initialCss);
  const dispatch = useDispatch();
  const { addDangerToast } = useToasts();

  useEffect(() => {
    if (activeTab !== '3') {
      return;
    }

    AceCssEditor.preload();

    SupersetClient.get({ endpoint: '/csstemplateasyncmodelview/api/read' })
      .then(({ json }) => {
        const templates = json.result.map(
          (row: { template_name: string; css: string }) => ({
            value: row.template_name,
            css: row.css,
            label: row.template_name,
          }),
        );

        setTemplates(templates);
      })
      .catch(() => {
        addDangerToast(
          t('An error occurred while fetching available CSS templates'),
        );
      });
  }, [addDangerToast, activeTab]);

  const handleChange = (css: string) => {
    setCssValue(css);
    dispatch(onChange());
    dispatch(updateCss(css));
    injectCustomCss(css);
  };

  const applyComponentStyles = (css: string) => {
    handleChange(cssValue ? `${cssValue}\n\n${css}` : css);
    setActiveTab('3');
  };

  const changeCssTemplate = (info: { key: Key }) => {
    const keyAsString = String(info.key);
    handleChange(keyAsString);
  };

  const renderTemplateSelector = () => {
    if (templates) {
      const menu = (
        <Menu onClick={changeCssTemplate}>
          {templates.map(template => (
            <Menu.Item key={template.css}>{template.label}</Menu.Item>
          ))}
        </Menu>
      );
      return (
        <AntdDropdown overlay={menu} placement="bottomRight">
          <Button>{t('Load a CSS template')}</Button>
        </AntdDropdown>
      );
    }
    return null;
  };
  // DODO added stop 55339193

  return (
    <div
      data-test="dashboard-builder-sidepane"
      css={css`
        position: sticky;
        right: 0;
        top: ${topOffset}px;
        height: calc(100vh - ${topOffset}px);
        width: ${BUILDER_PANE_WIDTH}px;
      `}
    >
      <div
        css={(theme: SupersetTheme) => css`
          position: absolute;
          height: 100%;
          width: ${BUILDER_PANE_WIDTH}px;
          box-shadow: -4px 0 4px 0 ${rgba(theme.colors.grayscale.dark2, 0.1)};
          background-color: ${theme.colors.grayscale.light5};
        `}
      >
        <Tabs
          data-test="dashboard-builder-component-pane-tabs-navigation"
          id="tabs"
          activeKey={activeTab} // DODO added 55339193
          onChange={setActiveTab} // DODO added 55339193
          css={(theme: SupersetTheme) => css`
            line-height: inherit;
            margin-top: ${theme.gridUnit * 2}px;
            height: 100%;

            & .ant-tabs-content-holder {
              height: 100%;
              & .ant-tabs-content {
                height: 100%;
              }
            }
          `}
        >
          <Tabs.TabPane
            key={1}
            tab={t('Charts')}
            css={css`
              height: 100%;
            `}
          >
            <SliceAdder />
          </Tabs.TabPane>
          <Tabs.TabPane key={2} tab={t('Layout elements')}>
            <NewTabs />
            <NewRow />
            <NewColumn />
            <NewHeader />
            <NewMarkdown />
            <NewDivider />
            {dashboardComponents
              .getAll()
              .map(({ key: componentKey, metadata }) => (
                <NewDynamicComponent
                  metadata={metadata}
                  componentKey={componentKey}
                />
              ))}
          </Tabs.TabPane>
          {/* DODO added start 55339193 */}
          <Tabs.TabPane key={3} tab="CSS">
            <StyledCssWrapper>
              <div className="css-editor-header">
                <h5>{t('Live CSS editor')}</h5>
                {renderTemplateSelector()}
              </div>
              <AceCssEditor
                className="css-editor"
                minLines={12}
                onChange={handleChange}
                height="calc(100% - 60px)"
                width="100%"
                editorProps={{ $blockScrolling: true }}
                enableLiveAutocompletion
                value={cssValue}
              />
            </StyledCssWrapper>
          </Tabs.TabPane>
          <Tabs.TabPane key={4} tab={t('Helper')}>
            <DashboardHelper applyComponentStyles={applyComponentStyles} />
          </Tabs.TabPane>
          {/* DODO added stop 55339193 */}
        </Tabs>
      </div>
    </div>
  );
};

export default BuilderComponentPane;
