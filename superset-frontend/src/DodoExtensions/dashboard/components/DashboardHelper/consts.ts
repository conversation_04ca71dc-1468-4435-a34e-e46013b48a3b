import { t } from '@superset-ui/core';
import { DashboardElement } from '.';

export const dashboardElements: DashboardElement[] = [
  {
    id: 'dashboard-header',
    title: t('Dashboard Header'),
    description: t('Main dashboard header container with title and actions'),
    selector: '.header-with-actions',
    category: 'layout',
    examples: [
      {
        title: t('Change header background'),
        code: `.header-with-actions {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}`,
      },
    ],
  },
  {
    id: 'dashboard-title',
    title: t('Dashboard Title'),
    description: t('The main title of the dashboard'),
    selector: '.header-with-actions .dynamic-title-input',
    category: 'layout',
    examples: [
      {
        title: t('Style dashboard title'),
        code: `.header-with-actions .dynamic-title-input {
  font-size: 28px;
  font-weight: bold;
  color: #1890ff;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}`,
      },
    ],
  },
  {
    id: 'dashboard-actions',
    title: t('Dashboard Actions'),
    description: t('The action button on the right side of the dashboard'),
    selector: '.header-with-actions .superset-button-tertiary',
    category: 'layout',
    examples: [
      {
        title: t('Style dashboard action button'),
        code: `.header-with-actions .superset-button-tertiary {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 8px;
}`,
      },
    ],
  },

  {
    id: 'dashboard-background',
    title: t('Dashboard Background'),
    description: t('Overall dashboard background and wrapper'),
    selector: '.dashboard-content',
    category: 'layout',
    examples: [
      {
        title: t('Change dashboard background'),
        code: `.dashboard-content {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}`,
      },
    ],
  },
  {
    id: 'chart-container',
    title: t('Chart Container'),
    description: t(
      'Individual chart containers. Use dashboard-chart-id-{ID} for specific charts',
    ),
    selector: '.dashboard-component-chart-holder',
    category: 'charts',
    examples: [
      {
        title: t('Style all chart containers'),
        code: `.dashboard-component-chart-holder {
  border: 2px solid #f0f0f0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  background: white;
}`,
      },
      {
        title: t('Style specific chart by ID'),
        code: `.dashboard-chart-id-123 {
  border: 2px solid #52c41a;
  background: #f6ffed;
}`,
      },
    ],
  },
  {
    id: 'chart-header',
    title: t('Chart Header'),
    description: t(
      'Header section of individual charts with title and controls',
    ),
    selector: '.slice-header',
    category: 'charts',
    examples: [
      {
        title: t('Style chart headers'),
        code: `.slice-header {
  background: #f8f9fa;
  border-bottom: 2px solid #e9ecef;
  padding: 12px 16px;
  border-radius: 8px 8px 0 0;
}`,
      },
      {
        title: t('Hide chart headers'),
        code: `.slice-header {
  display: none;
}`,
      },
    ],
  },
  {
    id: 'chart-title',
    title: t('Chart Title'),
    description: t('Title text within chart headers'),
    selector: '.header-title',
    category: 'charts',
    examples: [
      {
        title: t('Style chart titles'),
        code: `.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}`,
      },
      {
        title: t('Add icon before title'),
        code: `.header-title::before {
  content: "📊 ";
  margin-right: 8px;
}`,
      },
    ],
  },
  {
    id: 'filter-bar',
    title: t('Filter Bar'),
    description: t('Native filters sidebar'),
    selector: '.filter-bar',
    category: 'filters',
    examples: [
      {
        title: t('Style filter bar'),
        code: `.filter-bar {
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
  border-right: 3px solid #007bff;
}`,
      },
      {
        title: t('Change filter bar width'),
        code: `.filter-bar {
  width: 320px !important;
}`,
      },
    ],
  },
  {
    id: 'filter-controls',
    title: t('Filter Controls'),
    description: t('Individual filter input controls'),
    selector: '.filter-control',
    category: 'filters',
    examples: [
      {
        title: t('Style filter inputs'),
        code: `.filter-control .ant-select-selector,
.filter-control .ant-input {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: white;
}

.filter-control .ant-select-selector:hover,
.filter-control .ant-input:hover {
  border-color: #007bff;
}`,
      },
    ],
  },
  {
    id: 'dashboard-tabs',
    title: t('Dashboard Tabs'),
    description: t('Tab navigation in dashboard'),
    selector: '.dashboard-component-tabs',
    category: 'layout',
    examples: [
      {
        title: t('Style dashboard tabs'),
        code: `.dashboard-component-tabs {
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  padding: 16px;
}

.dashboard-component-tabs .ant-tabs-tab {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px 8px 0 0;
  margin-right: 4px;
}

.dashboard-component-tabs .ant-tabs-tab-active {
  background: white !important;
}`,
      },
    ],
  },

  {
    id: 'markdown-text',
    title: t('Markdown Text'),
    description: t('Text components and markdown elements in dashboard'),
    selector: '.dashboard-component-markdown',
    category: 'layout',
    examples: [
      {
        title: t('Style markdown text'),
        code: `.dashboard-component-markdown {
  background: #f8f9fa !important;
  border-left: 4px solid #007bff;
  padding: 16px;
  border-radius: 4px;
}

.dashboard-component-markdown h1,
.dashboard-component-markdown h2,
.dashboard-component-markdown h3 {
  color: #2c3e50;
  border-bottom: 2px solid #ecf0f1;
  padding-bottom: 8px;
}`,
      },
    ],
  },
  {
    id: 'dashboard-rows',
    title: t('Dashboard Rows'),
    description: t('Row containers that hold charts and other components'),
    selector: '.grid-row',
    category: 'layout',
    examples: [
      {
        title: t('Add row spacing and styling'),
        code: `.grid-row {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa !important;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}`,
      },
    ],
  },
  {
    id: 'dashboard-container',
    title: t('Dashboard Container'),
    description: t('Main container for dashboard'),
    selector: '.dashboard',
    category: 'layout',
    examples: [
      {
        title: t('Add grid background'),
        code: `.dashboard {
  background: coral;
  padding: 16px;
}`,
      },
    ],
  },
  {
    id: 'dashboard-grid',
    title: t('Dashboard Grid'),
    description: t('Main grid container for dashboard content'),
    selector: '.grid-container',
    category: 'layout',
    examples: [
      {
        title: t('Add container background'),
        code: `.grid-container {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
}`,
      },
    ],
  },
  {
    id: 'chart-controls',
    title: t('Chart Controls'),
    description: t(
      'Control buttons in chart headers (fullscreen, refresh, etc.)',
    ),
    selector: '.slice-header .header-controls',
    category: 'charts',
    examples: [
      {
        title: t('Style chart control buttons'),
        code: `.slice-header .header-controls button {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  color: #6c757d;
  transition: all 0.2s;
}

.slice-header .header-controls button:hover {
  background: #007bff;
  color: white;
  border-color: #007bff;
}`,
      },
      {
        title: t('Hide chart controls'),
        code: `.slice-header .header-controls {
  display: none;
}`,
      },
    ],
  },
  {
    id: 'chart-loading',
    title: t('Chart Loading State'),
    description: t('Loading indicators shown while charts are loading'),
    selector: '.chart-slice .loading',
    category: 'charts',
    examples: [
      {
        title: t('Customize loading spinner'),
        code: `.chart-slice .loading {
  background: red;
  backdrop-filter: blur(4px);
}
`,
      },
    ],
  },
  {
    id: 'filter-labels',
    title: t('Filter Labels'),
    description: t('Labels for filter inputs'),
    selector: '.filter-controls-wrapper h4',
    category: 'filters',
    examples: [
      {
        title: t('Style filter labels'),
        code: `.filter-controls-wrapper h4 {
  color: #007bff;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}`,
      },
    ],
  },
  {
    id: 'filter-inputs',
    title: t('Filter Inputs'),
    description: t('Individual filter input controls'),
    selector: '.filter-controls-wrapper .ant-select-selector',
    category: 'filters',
    examples: [
      {
        title: t('Style filter inputs'),
        code: `.filter-controls-wrapper .ant-select-selector {
  border: 2px solid #007bff !important;
  border-radius: 8px;
  background: white;
}

.filter-controls-wrapper .ant-select-selector:hover {
  border-color: #007bff !important;
},
`,
      },
    ],
  },
];
