import { t } from '@superset-ui/core';
import Icons from 'src/components/Icons';
import CopyToClipboard from 'src/components/CopyToClipboard';

const CopySelector = ({ sliceId }: { sliceId: number }) => (
  <CopyToClipboard
    copyNode={
      <div>
        <Icons.CopyOutlined iconSize="l" />
      </div>
    }
    text={`.dashboard-chart-id-${sliceId}`}
    tooltipText={t('Copy CSS selector')}
    tooltipPlacement="top"
    shouldShowText={false}
    wrapped={false}
  />
);

export default CopySelector;
