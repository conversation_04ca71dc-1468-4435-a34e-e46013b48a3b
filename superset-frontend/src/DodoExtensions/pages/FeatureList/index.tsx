import { useEffect, useState } from 'react';
import { styled, SupersetClient } from '@superset-ui/core';
import SubMenu from 'src/features/home/<USER>';
import { Switch } from 'src/components/Switch';
import Loading from 'src/components/Loading';
import { useToasts } from 'src/components/MessageToasts/withToasts';

interface Feature {
  id: number;
  name: string;
  description: string | null;
  value: boolean;
  created_on: string;
  changed_on: string | null;
}

const Wrapper = styled.div`
  padding-inline: ${({ theme }) => theme.gridUnit * 4}px;
`;

const List = styled.ul`
  margin: 0;
  padding: 0;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: ${({ theme }) => theme.gridUnit * 6}px;
`;

const FeatureItem = styled.li`
  padding: ${({ theme }) => theme.gridUnit * 4}px;
  background: ${({ theme }) => theme.colors.grayscale.light5};
  border: 1px solid ${({ theme }) => theme.colors.grayscale.light2};
  border-radius: ${({ theme }) => theme.borderRadius}px;
  list-style: none;
`;

const FeatureHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.gridUnit * 2}px;
`;

const FeatureName = styled.h3`
  margin: 0;
  font-size: ${({ theme }) => theme.typography.sizes.l}px;
  font-weight: ${({ theme }) => theme.typography.weights.bold};
`;

const FeatureDescription = styled.p`
  margin: 0;
  color: ${({ theme }) => theme.colors.grayscale.base};
  font-size: ${({ theme }) => theme.typography.sizes.m}px;
  line-height: 1.5;
`;

const FeatureDetails = styled.div`
  margin-top: ${({ theme }) => theme.gridUnit * 6}px;
  color: ${({ theme }) => theme.colors.grayscale.base};
  font-size: ${({ theme }) => theme.typography.sizes.s}px;

  p {
    margin: ${({ theme }) => theme.gridUnit * 2}px 0 0;
  }
`;

const formatDate = (dateStr: string | null): string => {
  if (!dateStr) return 'N/A';
  const date = new Date(dateStr);
  return date.toLocaleString('ru-RU');
};

const Feature = ({
  feature,
  addSuccessToast,
  addDangerToast,
}: {
  feature: Feature;
  addSuccessToast: (msg: string) => void;
  addDangerToast: (msg: string) => void;
}) => {
  const { id, name, description, value, changed_on, created_on } = feature;
  const [currentValue, setCurrentValue] = useState(value);
  const [currentChangedOn, setCurrentChangedOn] = useState(changed_on);
  const [saving, setSaving] = useState(false);

  const onChange = async (value: boolean) => {
    // Toggle the value before saving
    setCurrentValue(value);
    setSaving(true);

    try {
      const response = await SupersetClient.put({
        endpoint: `/api/v1/feature_flags/${id}/${value}`,
      });
      setCurrentChangedOn(response.json.result.changed_on);
      addSuccessToast('Feature saved successfully.');
    } catch (e) {
      // Revert the value if there is an error
      setCurrentValue(prev => !prev);
      addDangerToast('Error saving feature.');
    } finally {
      setSaving(false);
    }
  };

  return (
    <FeatureItem>
      <FeatureHeader>
        <FeatureName>{name}</FeatureName>
        <Switch
          checked={currentValue}
          onChange={onChange}
          loading={saving}
          disabled={saving}
        />
      </FeatureHeader>
      <FeatureDescription>{description}</FeatureDescription>
      <FeatureDetails>
        <p>Created on: {formatDate(created_on)}</p>
        <p>Changed on: {formatDate(currentChangedOn)}</p>
      </FeatureDetails>
    </FeatureItem>
  );
};

const FeatureList = () => {
  const [loading, setLoading] = useState(false);
  const [featuresData, setFeaturesData] = useState<Feature[] | null>(null);
  const { addSuccessToast, addDangerToast } = useToasts();

  useEffect(() => {
    const fetchFeatures = async () => {
      try {
        setLoading(true);
        const response = await SupersetClient.get({
          endpoint: '/api/v1/feature_flags',
        });
        const featuresData: Feature[] = response.json.result;
        setFeaturesData(featuresData);
      } catch (error) {
        addDangerToast('Error fetching features.');
      } finally {
        setLoading(false);
      }
    };
    fetchFeatures();
  }, [addDangerToast]);

  return (
    <>
      <SubMenu name="Features" />

      <Wrapper>
        {loading && <Loading position="inline-centered" />}
        <List>
          {featuresData?.map(feature => (
            <Feature
              key={feature.id}
              feature={feature}
              addSuccessToast={addSuccessToast}
              addDangerToast={addDangerToast}
            />
          ))}
        </List>
      </Wrapper>
    </>
  );
};

export default FeatureList;
