# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
import logging

logger = logging.getLogger(__name__)

FEATURE_FLAGS_CACHE_KEY = "feature_flags_cache"
FEATURE_FLAGS_CACHE_TIMEOUT = 3600


def get_feature_flags_from_db() -> dict[str, bool]:
    """Get feature flags from database"""
    # pylint: disable=import-outside-toplevel
    from superset.daos.feature_flag import FeatureFlagDAO

    db_flags: dict[str, bool] = {
        flag.name: flag.value for flag in FeatureFlagDAO.find_all()
    }
    return db_flags


def add_missing_flags_to_db(db_flags: dict[str, bool]) -> dict[str, bool]:
    """Add missing flags to database from FeatureFlagEnum"""
    # pylint: disable=import-outside-toplevel
    from superset import db
    from superset.daos.feature_flag import FeatureFlagDAO
    from superset.feature_flags.models import FeatureFlagEnum

    for flag in FeatureFlagEnum:
        if flag.name not in db_flags:
            FeatureFlagDAO.upsert(flag.name, False, flag.value)
            db_flags[flag.name] = False
    db.session.commit()  # pylint: disable=consider-using-transaction
    return db_flags


def get_feature_flags_from_cache() -> dict[str, bool] | None:
    """Get feature flags from cache"""
    # pylint: disable=import-outside-toplevel
    from superset.extensions import cache_manager

    return cache_manager.feature_flags_cache.get(FEATURE_FLAGS_CACHE_KEY)


def set_feature_flags_in_cache(flags: dict[str, bool]) -> None:
    """Set feature flags in cache"""
    # pylint: disable=import-outside-toplevel
    from superset.extensions import cache_manager

    cache_manager.feature_flags_cache.set(
        FEATURE_FLAGS_CACHE_KEY, flags, timeout=FEATURE_FLAGS_CACHE_TIMEOUT
    )


def apply_dynamic_flags(feature_flags: dict[str, bool]) -> dict[str, bool]:
    """
    Function to be used as GET_FEATURE_FLAGS_FUNC.

    This function:
    1. Starts with flags from config
    2. Reads flags from cache if available
    3. If cache is empty and app is ready, reads from DB and updates cache

    Args:
        feature_flags: The feature flags from config

    Returns:
        dict[str, bool]: The merged feature flags
    """
    # pylint: disable=import-outside-toplevel
    from superset.extensions import cache_manager

    try:
        flags = get_feature_flags_from_cache()
        if not flags and cache_manager.feature_flags_ready:
            logger.info("add_dynamic_flags: cache is empty, get from db")
            flags = update_cache_from_db()

        if flags:
            feature_flags.update(flags)
    except Exception as ex:  # pylint: disable=broad-except
        logger.warning("Failed to add dynamic flags: %s", ex)
        return feature_flags

    return feature_flags


def update_cache_from_db() -> dict[str, bool] | None:
    """Synchronize feature flags between DB and cache.

    This reads flags from the database and updates the cache.
    It should be called after the app is fully initialized.
    """
    # pylint: disable=import-outside-toplevel
    from superset.extensions import cache_manager

    try:
        db_flags = get_feature_flags_from_db()
        db_flags = add_missing_flags_to_db(db_flags)

        set_feature_flags_in_cache(db_flags)

        cache_manager.feature_flags_ready = True

        return db_flags
    except Exception as ex:  # pylint: disable=broad-except
        logger.warning("Failed to sync feature flags: %s", ex)
        return None
