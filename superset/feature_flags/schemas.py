# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
from marshmallow import fields, Schema

# OpenAPI documentation metadata
openapi_spec_methods_override = {
    "get": {
        "get": {
            "summary": "Get all feature flags",
            "description": "Returns all feature flags",
        },
    },
    "put": {
        "put": {
            "summary": "Update a feature flag",
            "description": "Updates the value of a feature flag",
        },
    },
}


class FeatureFlagSchema(Schema):
    """
    Schema for a single feature flag
    """

    id = fields.Integer()
    name = fields.String(required=True)
    value = fields.Boolean(required=True)
    description = fields.String(allow_none=True)
    created_on = fields.DateTime(dump_only=True)
    changed_on = fields.DateTime(dump_only=True)
    changed_by_fk = fields.Integer(dump_only=True)
