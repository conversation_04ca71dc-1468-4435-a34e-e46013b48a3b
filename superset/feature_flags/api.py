# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
import logging

from flask import Response
from flask_appbuilder.api import expose, protect, safe
from marshmallow import ValidationError

from superset import db
from superset.daos.feature_flag import FeatureFlagDAO
from superset.extensions import event_logger
from superset.feature_flags import flag_manager
from superset.feature_flags.models import FeatureFlagEnum
from superset.feature_flags.schemas import (
    FeatureFlagSchema,
    openapi_spec_methods_override,
)
from superset.utils.core import parse_boolean_string
from superset.views.base_api import BaseSupersetApi

logger = logging.getLogger(__name__)


class FeatureFlagRestApi(BaseSupersetApi):
    """
    API for managing feature flags
    """

    resource_name = "feature_flags"
    allow_browser_login = True
    openapi_spec_tag = "Feature Flags"
    openapi_spec_methods = openapi_spec_methods_override

    @expose("/", methods=["GET"])
    @protect()
    @safe
    @event_logger.log_this_with_context(
        action=lambda self, *args, **kwargs: f"{self.__class__.__name__}.get_list",
        log_to_statsd=False,
    )
    def get_list(self) -> Response:
        """Get all dynamic feature flags"""
        feature_flags = FeatureFlagDAO.find_all()

        schema = FeatureFlagSchema()
        result = schema.dump(feature_flags, many=True)

        return self.response(200, result=result)

    @expose("/<int:id>/<string:value>", methods=["PUT"])
    @protect()
    @safe
    @event_logger.log_this_with_context(
        action=lambda self, *args, **kwargs: f"{self.__class__.__name__}.put",
        log_to_statsd=False,
    )
    def put(self, id: int, value: str) -> Response:
        """Update a feature flag"""
        try:
            if not (feature_flag := FeatureFlagDAO.find_by_id(id)):
                return self.response_400(message=f"Unknown feature flag: {id}")

            feature_flag = FeatureFlagDAO.upsert(feature_flag.name, parse_boolean_string(value))
            db.session.commit()  # pylint: disable=consider-using-transaction
            flag_manager.update_cache_from_db()

            schema = FeatureFlagSchema()
            result = schema.dump(feature_flag)

            return self.response(200, result=result)
        except ValidationError as ex:
            return self.response_400(message=str(ex))
        except Exception as ex:  # pylint: disable=broad-except
            logger.exception("Error updating feature flag")
            return self.response_500(message=str(ex))
