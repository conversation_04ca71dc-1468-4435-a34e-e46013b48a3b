# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
from __future__ import annotations

from enum import Enum

from flask_appbuilder import Model
from sqlalchemy import <PERSON>olean, Column, Integer, String

from superset.models.helpers import AuditMixinNullable


class FeatureFlagEnum(Enum):
    AI_SEARCH = "AI поиск дашбордов"


class FeatureFlag(Model, AuditMixinNullable):
    """
    Model for storing dynamic feature flags in the database.
    """

    __tablename__ = "feature_flags"

    id = Column(Integer, primary_key=True)
    name = Column(String(255), unique=True, nullable=False)
    value = Column(Boolean, nullable=False)
    description = Column(String(500), nullable=True)

    def __repr__(self) -> str:
        return f"{self.name}: {self.value}"
