from flask_appbuilder import permission_name
from flask_appbuilder.api import expose
from flask_appbuilder.security.decorators import has_access

from superset.superset_typing import FlaskResponse

from .base import BaseSupersetView


class FeaturesView(BaseSupersetView):
    route_base = "/features"
    class_permission_name = "Features"

    @expose("/")
    @has_access
    @permission_name("read")
    def list(self) -> FlaskResponse:
        return super().render_app_template()
