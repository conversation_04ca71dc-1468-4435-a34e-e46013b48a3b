# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
import logging

from superset.daos.base import BaseDAO
from superset.feature_flags.models import FeatureFlag

logger = logging.getLogger(__name__)


class FeatureFlagDAO(BaseDAO[FeatureFlag]):
    @staticmethod
    def upsert(name: str, value: bool, description: str | None = None) -> FeatureFlag:
        """Upsert a feature flag

        Args:
            name: The name of the feature flag
            value: The value of the feature flag
            description: The description of the feature flag

        Returns:
            The feature flag
        """
        if feature_flag := FeatureFlagDAO.find_one_or_none(name=name):
            feature_flag.value = value
            if description:
                feature_flag.description = description
            return FeatureFlagDAO.update(feature_flag)
        return FeatureFlagDAO.create(
            FeatureFlag(name=name, value=value, description=description)
        )
