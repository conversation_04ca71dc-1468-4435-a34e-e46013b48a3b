# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements. See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License. You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""Create org_units table for storing organizational structure

Revision ID: 34fc36f98742
Revises: 08641b295ab2
Create Date: 2025-08-14 01:00:00.000000

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "34fc36f98742"
down_revision = "08641b295ab2"


def upgrade():
    op.create_table(
        "org_units",
        sa.Column("id", sa.String(length=64), primary_key=True, nullable=False),
        sa.Column("country_id", sa.Integer, nullable=False),
        sa.Column("department_uuid", sa.String(length=64), nullable=False),
        sa.Column("name", sa.String(length=512), nullable=False),
        sa.Column("alias", sa.String(length=512), nullable=True),
        sa.Column("type", sa.Integer, nullable=False),
        sa.Column("is_removed", sa.Boolean, nullable=False, default=False),
        sa.Column("version", sa.Integer, nullable=False),
        sa.Column("monolith_id", sa.String(length=64), nullable=True),
        sa.Column("dtc", sa.DateTime, nullable=False),
        sa.Column("dtu", sa.DateTime, nullable=False),
    )
    op.create_index("idx_org_units_department_uuid", "org_units", ["department_uuid"])
    op.create_index("idx_org_units_country_id", "org_units", ["country_id"])
    op.create_index("idx_org_units_country_monolith", "org_units", ["country_id", "monolith_id", "is_removed", "department_uuid"])


def downgrade():
    op.drop_index("idx_org_units_country_monolith", table_name="org_units")
    op.drop_index("idx_org_units_monolith_id", table_name="org_units")
    op.drop_index("idx_org_units_country_id", table_name="org_units")
    op.drop_index("idx_org_units_department_uuid", table_name="org_units")
    op.drop_table("org_units")
