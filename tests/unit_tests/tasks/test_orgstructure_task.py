from __future__ import annotations

import unittest
from unittest import mock

from superset.tasks.orgstructure import process_updates


class TestOrgstructureTask(unittest.TestCase):
    """Tests for the orgstructure Celery task."""

    @mock.patch('superset.tasks.orgstructure.process_kafka_messages')
    def test_process_updates(self, mock_process_kafka):
        """Test the process_updates Celery task."""
        # Mock process_kafka_messages to return 5
        mock_process_kafka.return_value = 5

        # Call the task
        result = process_updates()

        # Verify the task called process_kafka_messages
        mock_process_kafka.assert_called_once()

        # Verify the task returned the expected result
        self.assertEqual(result, {"processed": 5})

    @mock.patch('superset.tasks.orgstructure.process_kafka_messages')
    def test_process_updates_no_messages(self, mock_process_kafka):
        """Test the process_updates Celery task with no messages."""
        # Mock process_kafka_messages to return 0
        mock_process_kafka.return_value = 0

        # Call the task
        result = process_updates()

        # Verify the task called process_kafka_messages
        mock_process_kafka.assert_called_once()

        # Verify the task returned the expected result
        self.assertEqual(result, {"processed": 0})

    @mock.patch('superset.tasks.orgstructure.process_kafka_messages')
    def test_process_updates_error(self, mock_process_kafka):
        """Test the process_updates Celery task with an error."""
        # Mock process_kafka_messages to raise an exception
        mock_process_kafka.side_effect = Exception("Test error")

        # Call the task and expect an exception
        with self.assertRaises(Exception):
            process_updates()

        # Verify the task called process_kafka_messages
        mock_process_kafka.assert_called_once()
