from __future__ import annotations

import unittest
from unittest import mock

from superset.orgstructure.models import UnitSchema, UnitType, UnitTypeHeaders


class TestUnitSchema(unittest.TestCase):
    """Test cases for the UnitSchema class."""

    def test_schema_load_valid_data(self):
        """Test that valid data loads correctly."""
        # Test data with camelCase keys
        test_data = {
            "Id": "TEST123",
            "CountryId": 123,
            "DepartmentId": "dep-uuid-123",
            "Name": "Test Unit",
            "Alias": "Test",
            "Type": UnitType.PIZZERIA.value,
            "IsRemoved": False,
            "Version": 5,
            "MonolithId": "m123"
        }

        # Load the data through schema
        schema = UnitSchema()
        result = schema.load(test_data)

        # Verify the result has snake_case keys
        self.assertEqual(result["id"], "TEST123")
        self.assertEqual(result["country_id"], 123)
        self.assertEqual(result["department_uuid"], "dep-uuid-123")
        self.assertEqual(result["name"], "Test Unit")
        self.assertEqual(result["alias"], "Test")
        self.assertEqual(result["type"], UnitType.PIZZERIA.value)
        self.assertEqual(result["is_removed"], False)
        self.assertEqual(result["version"], 5)
        self.assertEqual(result["monolith_id"], "m123")

    def test_schema_load_with_missing_fields(self):
        """Test that schema validation catches missing required fields."""
        # Test data with missing required fields
        test_data = {
            "Id": "TEST123",
            "Name": "Test Unit",
        }

        # Load the data through schema
        schema = UnitSchema()

        # Should raise validation error
        with self.assertRaises(Exception):
            schema.load(test_data)

    def test_schema_unknown_fields(self):
        """Test that schema ignores unknown fields."""
        # Test data with extra fields
        test_data = {
            "Id": "TEST123",
            "CountryId": 123,
            "DepartmentId": "dep-uuid-123",
            "Name": "Test Unit",
            "Alias": "Test",
            "Type": UnitType.PIZZERIA.value,
            "IsRemoved": False,
            "Version": 5,
            "MonolithId": "m123",
            "ExtraField": "should be ignored"
        }

        # Load the data through schema
        schema = UnitSchema()
        result = schema.load(test_data)

        # Verify the extra field is not in the result
        self.assertNotIn("extra_field", result)
        self.assertNotIn("ExtraField", result)
