from __future__ import annotations

import json
import unittest
from unittest import mock

from confluent_kafka import <PERSON>f<PERSON><PERSON><PERSON><PERSON>, KafkaException, OFFSET_INVALID
from superset.orgstructure.kafka import build_consumer, process_kafka_messages, set_offset


class TestKafka(unittest.TestCase):
    """Test cases for the kafka module."""

    @mock.patch('superset.orgstructure.kafka.current_app', new_callable=mock.MagicMock)
    def test_build_consumer_normal(self, mock_app):
        """Test building a consumer for normal processing."""
        # Mock configuration
        mock_app.config.get.return_value = {
            "bootstrap.servers": "test-server:9092",
            "group.id": "test-group",
            "auto.offset.reset": "earliest",
        }

        consumer = build_consumer(read_from_beginning=False)

        # Verify config was read correctly
        mock_app.config.get.assert_called_once_with("KAFKA_CONSUMER_CONFIG", {})

        # Test the consumer has the correct config
        # This is a bit tricky since Consumer is a real class, but we can check if it was created
        self.assertIsNotNone(consumer)

    @mock.patch('superset.orgstructure.kafka.current_app', new_callable=mock.MagicMock)
    @mock.patch('superset.orgstructure.kafka.uuid')
    def test_build_consumer_from_beginning(self, mock_uuid, mock_app):
        """Test building a consumer for reading from beginning."""
        # Mock configuration and UUID
        mock_app.config.get.return_value = {
            "bootstrap.servers": "test-server:9092",
        }
        mock_uuid.uuid4.return_value = "test-uuid"

        consumer = build_consumer(read_from_beginning=True)

        # Verify config was read correctly
        mock_app.config.get.assert_called_once_with("KAFKA_CONSUMER_CONFIG", {})

        # Test the consumer was created
        self.assertIsNotNone(consumer)

    @mock.patch('superset.orgstructure.kafka.Consumer')
    @mock.patch('superset.orgstructure.kafka.TopicPartition')
    def test_set_offset_no_committed(self, mock_topic_partition_class, mock_consumer_class):
        """Test setting offset when no offset is committed."""
        # Mock the consumer
        consumer = mock.MagicMock()

        # Mock assignment and watermarks
        topic_partition = mock.MagicMock()
        consumer.assignment.return_value = [topic_partition]

        # Mock committed with OFFSET_INVALID
        committed_tp = mock.MagicMock()
        committed_tp.offset = OFFSET_INVALID
        consumer.committed.return_value = [committed_tp]

        # Mock watermark offsets
        consumer.get_watermark_offsets.return_value = (0, 1000)

        # Mock topic partition for commit
        mock_topic_partition = mock.MagicMock()
        mock_topic_partition_class.return_value = mock_topic_partition

        # Call the function
        set_offset(consumer)

        # Verify consumer.commit was called with the high watermark
        consumer.commit.assert_called_once()
        args, kwargs = consumer.commit.call_args
        self.assertEqual(kwargs["asynchronous"], False)
        self.assertIn("offsets", kwargs)
        self.assertIsInstance(kwargs["offsets"], list)
        self.assertEqual(len(kwargs["offsets"]), 1)

        # Verify consumer.get_watermark_offsets was called
        consumer.get_watermark_offsets.assert_called_once_with(topic_partition)

    @mock.patch('superset.orgstructure.kafka.Consumer')
    def test_set_offset_with_committed(self, mock_consumer_class):
        """Test setting offset when an offset is already committed."""
        # Mock the consumer
        consumer = mock.MagicMock()

        # Mock assignment
        topic_partition = mock.MagicMock()
        consumer.assignment.return_value = [topic_partition]

        # Mock committed with a valid offset
        committed_tp = mock.MagicMock()
        committed_tp.offset = 500
        consumer.committed.return_value = [committed_tp]

        # Call the function
        set_offset(consumer)

        # Verify consumer.commit was not called
        consumer.commit.assert_not_called()

    @mock.patch('superset.orgstructure.kafka.build_consumer')
    @mock.patch('superset.orgstructure.kafka.current_app', new_callable=mock.MagicMock)
    @mock.patch('superset.orgstructure.kafka.set_offset')
    def test_process_kafka_messages_no_messages(self, mock_set_offset, mock_app, mock_build_consumer):
        """Test processing with no messages."""
        # Mock configuration
        mock_app.config.get.return_value = "orgstructure"

        # Mock the consumer
        consumer = mock.MagicMock()
        mock_build_consumer.return_value = consumer

        # Mock consumer.poll to return None (no message)
        consumer.poll.return_value = None

        # Mock consumer.close to avoid errors
        consumer.close = mock.MagicMock()

        # Set up the poll to return None once, then a message with an EOF error
        def poll_side_effect(timeout):
            poll_side_effect.counter += 1
            if poll_side_effect.counter == 1:
                return None
            else:
                # Return a message with KafkaError._PARTITION_EOF
                msg = mock.MagicMock()
                error_mock = mock.MagicMock()
                error_mock.code.return_value = KafkaError._PARTITION_EOF
                msg.error.return_value = error_mock
                return msg
        poll_side_effect.counter = 0
        consumer.poll.side_effect = poll_side_effect

        # Call the function
        result = process_kafka_messages(read_from_beginning=False)

        # Verify the function returned 0 processed messages
        self.assertEqual(result, 0)

        # Verify the consumer was subscribed to the topic
        consumer.subscribe.assert_called_once_with(["orgstructure"])

        # Verify set_offset was called
        mock_set_offset.assert_called_once_with(consumer)

        # Verify the consumer was closed
        consumer.close.assert_called_once()

    @mock.patch('superset.orgstructure.kafka.build_consumer')
    @mock.patch('superset.orgstructure.kafka.current_app', new_callable=mock.MagicMock)
    @mock.patch('superset.orgstructure.kafka.parse_unit')
    @mock.patch('superset.orgstructure.kafka.OrgUnitDAO')
    def test_process_kafka_messages_with_message(self, mock_dao, mock_parse_unit, mock_app, mock_build_consumer):
        """Test processing with a valid message."""
        # Mock configuration
        mock_app.config.get.return_value = "orgstructure"

        # Mock the consumer
        consumer = mock.MagicMock()
        mock_build_consumer.return_value = consumer

        # Create a mock message
        msg = mock.MagicMock()
        msg.error.return_value = None
        msg.headers.return_value = [("Event-Type", b"Dodo.DataCatalog.Contracts.OrganizationalStructure.v1.Unit")]
        msg.value.return_value = json.dumps({"Id": "TEST123"}).encode("utf-8")

        # Set up poll to return one message then EOF
        def poll_side_effect(timeout):
            poll_side_effect.counter += 1
            if poll_side_effect.counter == 1:
                return msg
            else:
                # Return EOF
                eof_msg = mock.MagicMock()
                error_mock = mock.MagicMock()
                error_mock.code.return_value = KafkaError._PARTITION_EOF
                eof_msg.error.return_value = error_mock
                return eof_msg
        poll_side_effect.counter = 0
        consumer.poll.side_effect = poll_side_effect

        # Mock parse_unit to return a valid unit
        mock_parse_unit.return_value = {"id": "TEST123", "version": 1}

        # Mock DAO to return True for successful upsert
        mock_dao.upsert_from_payload.return_value = True

        # Call the function
        result = process_kafka_messages(read_from_beginning=False)

        # Verify the function returned 1 processed message
        self.assertEqual(result, 1)

        # Verify parse_unit was called with the right data
        mock_parse_unit.assert_called_once()

        # Verify DAO.upsert_from_payload was called
        mock_dao.upsert_from_payload.assert_called_once_with({"id": "TEST123", "version": 1})

    @mock.patch('superset.orgstructure.kafka.build_consumer')
    @mock.patch('superset.orgstructure.kafka.current_app', new_callable=mock.MagicMock)
    def test_process_kafka_messages_with_error(self, mock_app, mock_build_consumer):
        """Test handling of Kafka error."""
        # Mock configuration
        mock_app.config.get.return_value = "orgstructure"

        # Mock the consumer
        consumer = mock.MagicMock()
        mock_build_consumer.return_value = consumer

        # Mock consumer to raise KafkaException
        consumer.subscribe.side_effect = KafkaException("Test error")

        # Call the function
        result = process_kafka_messages(read_from_beginning=False)

        # Verify the function returned 0 processed messages
        self.assertEqual(result, 0)
